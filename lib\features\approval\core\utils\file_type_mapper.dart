/// -----
/// file_type_mapper.dart
///
/// 文件类型映射工具类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 文件类型映射信息
class FileTypeInfo {
  final String fileId;
  final String iconPath;
  final String displayName;

  const FileTypeInfo({
    required this.fileId,
    required this.iconPath,
    required this.displayName,
  });
}

/// 文件类型映射工具类
class FileTypeMapper {
  /// 文件类型映射表
  static const Map<String, FileTypeInfo> _fileTypeMap = {
    '三方协议': FileTypeInfo(
      fileId: 'tripartite_agreement',
      iconPath: 'assets/images/upload_blue_doc_icon.png',
      displayName: '三方协议',
    ),
    '告家长通知书': FileTypeInfo(
      fileId: 'parent_notice',
      iconPath: 'assets/images/upload_pink_pdf_icon.png',
      displayName: '告家长通知书',
    ),
    '实习保险单': FileTypeInfo(
      fileId: 'insurance',
      iconPath: 'assets/images/upload_purple_pdf_icon.png',
      displayName: '实习保险单',
    ),
    '自助协议申请表': FileTypeInfo(
      fileId: 'self_agreement',
      iconPath: 'assets/images/upload_orange_pdf_icon.png',
      displayName: '自助协议申请表',
    ),
  };

  /// 默认文件类型信息
  static const FileTypeInfo _defaultFileTypeInfo = FileTypeInfo(
    fileId: 'unknown',
    iconPath: 'assets/images/upload_blue_doc_icon.png',
    displayName: '未知文件',
  );

  /// 根据文件类型获取文件信息
  static FileTypeInfo getFileTypeInfo(String fileType) {
    return _fileTypeMap[fileType] ?? _defaultFileTypeInfo;
  }

  /// 根据文件ID获取文件信息
  static FileTypeInfo? getFileTypeInfoById(String fileId) {
    for (final info in _fileTypeMap.values) {
      if (info.fileId == fileId) {
        return info;
      }
    }
    return null;
  }

  /// 获取所有支持的文件类型
  static List<String> getSupportedFileTypes() {
    return _fileTypeMap.keys.toList();
  }

  /// 获取所有文件类型信息
  static List<FileTypeInfo> getAllFileTypeInfos() {
    return _fileTypeMap.values.toList();
  }
}

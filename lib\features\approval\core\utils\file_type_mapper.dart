/// -----
/// file_type_mapper.dart
///
/// 文件类型映射工具类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 文件类型映射信息
class FileTypeInfo {
  final String fileId;
  final String iconPath;
  final String displayName;

  const FileTypeInfo({
    required this.fileId,
    required this.iconPath,
    required this.displayName,
  });
}

/// 文件类型映射工具类
class FileTypeMapper {
  /// 文件类型映射表
  static const Map<String, FileTypeInfo> _fileTypeMap = {
    // 原有的映射
    '三方协议': FileTypeInfo(
      fileId: 'tripartite_agreement',
      iconPath: 'assets/images/upload_blue_doc_icon.png',
      displayName: '三方协议',
    ),
    '告家长通知书': FileTypeInfo(
      fileId: 'parent_notice',
      iconPath: 'assets/images/upload_pink_pdf_icon.png',
      displayName: '告家长通知书',
    ),
    '实习保险单': FileTypeInfo(
      fileId: 'insurance',
      iconPath: 'assets/images/upload_purple_pdf_icon.png',
      displayName: '实习保险单',
    ),
    '自助协议申请表': FileTypeInfo(
      fileId: 'self_agreement',
      iconPath: 'assets/images/upload_orange_pdf_icon.png',
      displayName: '自助协议申请表',
    ),
    // 新增API返回的文件类型映射
    '家长知情同意书': FileTypeInfo(
      fileId: 'parent_notice',
      iconPath: 'assets/images/upload_pink_pdf_icon.png',
      displayName: '家长知情同意书',
    ),
    '保险文件': FileTypeInfo(
      fileId: 'insurance',
      iconPath: 'assets/images/upload_purple_pdf_icon.png',
      displayName: '保险文件',
    ),
    '测试文件': FileTypeInfo(
      fileId: 'test_file',
      iconPath: 'assets/images/upload_blue_doc_icon.png',
      displayName: '测试文件',
    ),
  };

  /// 根据文件类型获取文件信息
  static FileTypeInfo getFileTypeInfo(String fileType) {
    // 如果在映射表中找到，直接返回
    if (_fileTypeMap.containsKey(fileType)) {
      return _fileTypeMap[fileType]!;
    }

    // 如果没有找到，创建一个动态的FileTypeInfo，使用API返回的文件类型名称
    return FileTypeInfo(
      fileId: _generateFileId(fileType),
      iconPath: 'assets/images/upload_blue_doc_icon.png', // 使用默认图标
      displayName: fileType, // 直接使用API返回的文件类型名称
    );
  }

  /// 根据文件类型生成文件ID
  static String _generateFileId(String fileType) {
    if (fileType.isEmpty) {
      return 'unknown_file';
    }

    // 将中文文件类型转换为英文ID
    String id = fileType
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w]'), '_') // 将非字母数字字符替换为下划线
        .replaceAll(RegExp('_+'), '_') // 将多个连续下划线替换为单个下划线
        .replaceAll(RegExp(r'^_|_$'), ''); // 移除开头和结尾的下划线

    // 如果生成的ID为空，使用默认值
    return id.isEmpty ? 'unknown_file' : id;
  }

  /// 根据文件ID获取文件信息
  static FileTypeInfo? getFileTypeInfoById(String fileId) {
    for (final info in _fileTypeMap.values) {
      if (info.fileId == fileId) {
        return info;
      }
    }
    return null;
  }

  /// 获取所有支持的文件类型
  static List<String> getSupportedFileTypes() {
    return _fileTypeMap.keys.toList();
  }

  /// 获取所有文件类型信息
  static List<FileTypeInfo> getAllFileTypeInfos() {
    return _fileTypeMap.values.toList();
  }
}

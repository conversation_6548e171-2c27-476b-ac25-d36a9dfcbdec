/// -----
/// approval_injection.dart
///
/// 审批模块依赖注入配置
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/network/network_info.dart';
import '../data/datasources/remote/file_approval_remote_data_source.dart';
import '../data/datasources/remote/file_approval_remote_data_source_impl.dart';
import '../data/repositories/file_approval_repository_impl.dart';
import '../domain/repositories/file_approval_repository.dart';
import '../domain/usecases/get_file_approval_list_usecase.dart';
import '../presentation/bloc/file_approval_list/file_approval_list_bloc.dart';

final getIt = GetIt.instance;

/// 初始化审批模块依赖
/// 
/// 注册审批模块的数据源、仓库、用例和BLoC
Future<void> setupApprovalDependencies() async {
  // 数据源
  getIt.registerLazySingleton<FileApprovalRemoteDataSource>(
    () => FileApprovalRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 仓库
  getIt.registerLazySingleton<FileApprovalRepository>(
    () => FileApprovalRepositoryImpl(
      getIt<FileApprovalRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerFactory<GetFileApprovalListUseCase>(
    () => GetFileApprovalListUseCase(getIt<FileApprovalRepository>()),
  );

  // BLoC
  getIt.registerFactory<FileApprovalListBloc>(
    () => FileApprovalListBloc(
      getFileApprovalListUseCase: getIt<GetFileApprovalListUseCase>(),
    ),
  );
}

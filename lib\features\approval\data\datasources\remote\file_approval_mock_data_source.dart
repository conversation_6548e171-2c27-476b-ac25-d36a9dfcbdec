/// -----
/// file_approval_mock_data_source.dart
///
/// 文件审批模拟数据源（用于测试和演示）
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../../../core/utils/logger.dart';
import '../../models/file_approval_list_item_model.dart';
import 'file_approval_remote_data_source.dart';

/// 文件审批模拟数据源
class FileApprovalMockDataSource implements FileApprovalRemoteDataSource {
  @override
  Future<List<FileApprovalListItemModel>> getFileApprovalList(String planId) async {
    Logger.info('FileApprovalMockDataSource', '开始获取模拟文件审批列表，planId: $planId');
    
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 模拟数据
    final mockData = [
      const FileApprovalListItemModel(
        description: '老师文件审批',
        alreadyCount: 15,
        fileCode: 1001,
        fileType: '三方协议',
        shouldCount: 25,
      ),
      const FileApprovalListItemModel(
        description: '老师文件审批',
        alreadyCount: 20,
        fileCode: 1002,
        fileType: '告家长通知书',
        shouldCount: 25,
      ),
      const FileApprovalListItemModel(
        description: '老师文件审批',
        alreadyCount: 18,
        fileCode: 1003,
        fileType: '实习保险单',
        shouldCount: 25,
      ),
      const FileApprovalListItemModel(
        description: '老师文件审批',
        alreadyCount: 12,
        fileCode: 1004,
        fileType: '自助协议申请表',
        shouldCount: 25,
      ),
    ];

    Logger.info('FileApprovalMockDataSource', '成功获取${mockData.length}个模拟文件审批项');
    return mockData;
  }
}

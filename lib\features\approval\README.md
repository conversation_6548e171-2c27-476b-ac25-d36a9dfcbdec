# 文件审批模块

## 概述

文件审批模块实现了教师端文件审批功能，包括获取文件审批列表、显示审批进度统计等功能。

## 功能特性

- ✅ 获取文件审批列表数据
- ✅ 显示审批进度统计（已审批/总数）
- ✅ 支持下拉刷新
- ✅ 错误处理和重试机制
- ✅ 加载状态显示
- ✅ 文件类型映射和图标显示
- ✅ 响应式UI设计

## API 接口

### 获取文件审批列表

**接口地址：** `GET /internshipservice/v1/internship/teacher/file/approveList`

**请求参数：**
- `planId` (string): 实习计划ID

**响应数据结构：**
```json
{
  "resultCode": "0",
  "resultMsg": "success",
  "data": [
    {
      "fileType": "三方协议",
      "fileCode": 1,
      "alreadyCount": 1,
      "shouldCount": 2
    },
    {
      "fileType": "家长知情同意书",
      "fileCode": 2,
      "alreadyCount": 0,
      "shouldCount": 2
    },
    {
      "fileType": "保险文件",
      "fileCode": 3,
      "alreadyCount": 2,
      "shouldCount": 2
    },
    {
      "fileType": "测试文件",
      "fileCode": 4,
      "alreadyCount": 0,
      "shouldCount": 1
    }
  ]
}
```

## 架构设计

本模块遵循 Clean Architecture 原则，包含以下层次：

### Domain Layer (领域层)
- `entities/file_approval_list_item.dart` - 文件审批列表项实体
- `repositories/file_approval_repository.dart` - 文件审批仓库接口
- `usecases/get_file_approval_list_usecase.dart` - 获取文件审批列表用例

### Data Layer (数据层)
- `models/file_approval_list_item_model.dart` - 文件审批列表项数据模型
- `datasources/remote/file_approval_remote_data_source.dart` - 远程数据源接口
- `datasources/remote/file_approval_remote_data_source_impl.dart` - 远程数据源实现
- `repositories/file_approval_repository_impl.dart` - 文件审批仓库实现

### Presentation Layer (表现层)
- `bloc/file_approval_list/` - BLoC 状态管理
- `pages/file_approval_screen.dart` - 文件审批页面

### Core (核心工具)
- `utils/file_type_mapper.dart` - 文件类型映射工具

## 使用方法

### 1. 依赖注入配置

在 `lib/core/config/injection/injection.dart` 中已添加审批模块的依赖注入：

```dart
await setupApprovalDependencies();
```

### 2. 页面使用

文件审批页面会自动：
1. 从全局状态获取当前实习计划ID
2. 调用API获取文件审批列表
3. 显示审批进度统计
4. 支持下拉刷新和错误重试

### 3. 数据流

```
UI Event → BLoC → UseCase → Repository → DataSource → API
                     ↓
UI State ← BLoC ← UseCase ← Repository ← DataSource ← Response
```

## 文件类型映射

支持的文件类型及其对应的图标：

| 文件类型 | 文件ID | 图标 |
|---------|--------|------|
| 三方协议 | tripartite_agreement | upload_blue_doc_icon.png |
| 告家长通知书 | parent_notice | upload_pink_pdf_icon.png |
| 家长知情同意书 | parent_notice | upload_pink_pdf_icon.png |
| 实习保险单 | insurance | upload_purple_pdf_icon.png |
| 保险文件 | insurance | upload_purple_pdf_icon.png |
| 自助协议申请表 | self_agreement | upload_orange_pdf_icon.png |
| 测试文件 | test_file | upload_blue_doc_icon.png |

**智能映射机制：**
- 对于预定义的文件类型，使用对应的图标和文件ID
- 对于未预定义的文件类型，自动使用 API 返回的文件类型名称作为显示名称
- 未知文件类型使用默认蓝色图标，并生成对应的文件ID

## 测试

运行测试：
```bash
flutter test test/features/approval/file_approval_list_test.dart
```

测试覆盖：
- 实体类属性和方法
- 数据模型转换
- 文件类型映射功能

## 注意事项

1. 确保在使用前已正确配置依赖注入
2. 页面会监听全局实习计划状态变化，自动刷新数据
3. 网络错误会显示友好的错误提示和重试按钮
4. 支持下拉刷新功能，提升用户体验

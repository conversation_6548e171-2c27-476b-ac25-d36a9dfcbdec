/// -----
/// api_integration_test.dart
///
/// API 集成测试，验证实际 API 响应的处理
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/approval/data/models/file_approval_list_item_model.dart';
import 'package:flutter_demo/features/approval/core/utils/file_type_mapper.dart';

void main() {
  group('API Integration Tests', () {
    test('should correctly parse actual API response data', () {
      // Arrange - 模拟实际的 API 响应数据
      final apiResponseData = [
        {
          "fileType": "三方协议",
          "fileCode": 1,
          "alreadyCount": 1,
          "shouldCount": 2
        },
        {
          "fileType": "家长知情同意书",
          "fileCode": 2,
          "alreadyCount": 0,
          "shouldCount": 2
        },
        {
          "fileType": "保险文件",
          "fileCode": 3,
          "alreadyCount": 2,
          "shouldCount": 2
        },
        {
          "fileType": "测试文件",
          "fileCode": 4,
          "alreadyCount": 0,
          "shouldCount": 1
        }
      ];

      // Act - 解析数据
      final models = apiResponseData
          .map((json) => FileApprovalListItemModel.fromJson(json))
          .toList();

      // Assert - 验证解析结果
      expect(models.length, 4);

      // 验证第一个项目（三方协议）
      expect(models[0].fileType, '三方协议');
      expect(models[0].fileCode, 1);
      expect(models[0].alreadyCount, 1);
      expect(models[0].shouldCount, 2);
      expect(models[0].statisticsText, '1/2');
      expect(models[0].description, '老师文件审批'); // 默认值

      // 验证第二个项目（家长知情同意书）
      expect(models[1].fileType, '家长知情同意书');
      expect(models[1].fileCode, 2);
      expect(models[1].alreadyCount, 0);
      expect(models[1].shouldCount, 2);
      expect(models[1].statisticsText, '0/2');

      // 验证第三个项目（保险文件）
      expect(models[2].fileType, '保险文件');
      expect(models[2].fileCode, 3);
      expect(models[2].alreadyCount, 2);
      expect(models[2].shouldCount, 2);
      expect(models[2].statisticsText, '2/2');
      expect(models[2].isCompleted, true); // 已完成

      // 验证第四个项目（测试文件）
      expect(models[3].fileType, '测试文件');
      expect(models[3].fileCode, 4);
      expect(models[3].alreadyCount, 0);
      expect(models[3].shouldCount, 1);
      expect(models[3].statisticsText, '0/1');
    });

    test('should correctly map file types to display info', () {
      // Arrange - API 返回的文件类型
      final fileTypes = ['三方协议', '家长知情同意书', '保险文件', '测试文件'];

      // Act & Assert
      for (final fileType in fileTypes) {
        final fileTypeInfo = FileTypeMapper.getFileTypeInfo(fileType);
        
        // 验证显示名称应该是 API 返回的文件类型名称
        expect(fileTypeInfo.displayName, fileType);
        
        // 验证文件ID不为空
        expect(fileTypeInfo.fileId.isNotEmpty, true);
        
        // 验证图标路径不为空
        expect(fileTypeInfo.iconPath.isNotEmpty, true);
        
        print('文件类型: $fileType -> 显示名称: ${fileTypeInfo.displayName}, 文件ID: ${fileTypeInfo.fileId}');
      }
    });

    test('should handle unknown file types gracefully', () {
      // Arrange
      const unknownFileType = '新的未知文件类型';

      // Act
      final fileTypeInfo = FileTypeMapper.getFileTypeInfo(unknownFileType);

      // Assert
      expect(fileTypeInfo.displayName, unknownFileType); // 应该使用原始名称
      expect(fileTypeInfo.fileId.isNotEmpty, true); // 应该生成一个ID
      expect(fileTypeInfo.iconPath, 'assets/images/upload_blue_doc_icon.png'); // 使用默认图标
      
      print('未知文件类型: $unknownFileType -> 显示名称: ${fileTypeInfo.displayName}, 文件ID: ${fileTypeInfo.fileId}');
    });

    test('should generate consistent file IDs for same file types', () {
      // Arrange
      const fileType = '测试文件类型';

      // Act
      final info1 = FileTypeMapper.getFileTypeInfo(fileType);
      final info2 = FileTypeMapper.getFileTypeInfo(fileType);

      // Assert
      expect(info1.fileId, info2.fileId); // 相同的文件类型应该生成相同的ID
      expect(info1.displayName, info2.displayName);
    });
  });
}

/// -----
/// file_approval_list_test.dart
///
/// 文件审批列表功能测试
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/approval/domain/entities/file_approval_list_item.dart';
import 'package:flutter_demo/features/approval/data/models/file_approval_list_item_model.dart';
import 'package:flutter_demo/features/approval/core/utils/file_type_mapper.dart';

void main() {
  group('FileApprovalListItem', () {
    test('should create entity with correct properties', () {
      // Arrange
      const item = FileApprovalListItem(
        description: '老师文件审批',
        alreadyCount: 15,
        fileCode: 1001,
        fileType: '三方协议',
        shouldCount: 25,
      );

      // Assert
      expect(item.description, '老师文件审批');
      expect(item.alreadyCount, 15);
      expect(item.fileCode, 1001);
      expect(item.fileType, '三方协议');
      expect(item.shouldCount, 25);
      expect(item.statisticsText, '15/25');
      expect(item.isCompleted, false);
      expect(item.progressPercentage, 0.6);
    });

    test('should return correct completion status', () {
      // Arrange
      const completedItem = FileApprovalListItem(
        description: '老师文件审批',
        alreadyCount: 25,
        fileCode: 1001,
        fileType: '三方协议',
        shouldCount: 25,
      );

      const incompleteItem = FileApprovalListItem(
        description: '老师文件审批',
        alreadyCount: 15,
        fileCode: 1001,
        fileType: '三方协议',
        shouldCount: 25,
      );

      // Assert
      expect(completedItem.isCompleted, true);
      expect(incompleteItem.isCompleted, false);
    });
  });

  group('FileApprovalListItemModel', () {
    test('should convert from JSON correctly', () {
      // Arrange
      final json = {
        'description': '老师文件审批',
        'alreadyCount': 15,
        'fileCode': 1001,
        'fileType': '三方协议',
        'shouldCount': 25,
      };

      // Act
      final model = FileApprovalListItemModel.fromJson(json);

      // Assert
      expect(model.description, '老师文件审批');
      expect(model.alreadyCount, 15);
      expect(model.fileCode, 1001);
      expect(model.fileType, '三方协议');
      expect(model.shouldCount, 25);
    });

    test('should convert to JSON correctly', () {
      // Arrange
      const model = FileApprovalListItemModel(
        description: '老师文件审批',
        alreadyCount: 15,
        fileCode: 1001,
        fileType: '三方协议',
        shouldCount: 25,
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['description'], '老师文件审批');
      expect(json['alreadyCount'], 15);
      expect(json['fileCode'], 1001);
      expect(json['fileType'], '三方协议');
      expect(json['shouldCount'], 25);
    });

    test('should convert to entity correctly', () {
      // Arrange
      const model = FileApprovalListItemModel(
        description: '老师文件审批',
        alreadyCount: 15,
        fileCode: 1001,
        fileType: '三方协议',
        shouldCount: 25,
      );

      // Act
      final entity = model.toEntity();

      // Assert
      expect(entity.description, '老师文件审批');
      expect(entity.alreadyCount, 15);
      expect(entity.fileCode, 1001);
      expect(entity.fileType, '三方协议');
      expect(entity.shouldCount, 25);
    });
  });

  group('FileTypeMapper', () {
    test('should return correct file type info for known types', () {
      // Act
      final info = FileTypeMapper.getFileTypeInfo('三方协议');

      // Assert
      expect(info.fileId, 'tripartite_agreement');
      expect(info.iconPath, 'assets/images/upload_blue_doc_icon.png');
      expect(info.displayName, '三方协议');
    });

    test('should return default info for unknown types', () {
      // Act
      final info = FileTypeMapper.getFileTypeInfo('未知文件类型');

      // Assert
      expect(info.fileId, 'unknown');
      expect(info.iconPath, 'assets/images/upload_blue_doc_icon.png');
      expect(info.displayName, '未知文件');
    });

    test('should return all supported file types', () {
      // Act
      final types = FileTypeMapper.getSupportedFileTypes();

      // Assert
      expect(types.length, 4);
      expect(types.contains('三方协议'), true);
      expect(types.contains('告家长通知书'), true);
      expect(types.contains('实习保险单'), true);
      expect(types.contains('自助协议申请表'), true);
    });
  });
}

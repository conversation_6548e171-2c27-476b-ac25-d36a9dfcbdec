/// -----------------------------------------------------------------------------
/// test_plan_integration.dart
///
/// 测试实习计划集成功能
/// 用于验证实习计划列表的加载和切换功能是否正常工作
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'core/widgets/course_header_section.dart';
import 'features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'features/internship/presentation/bloc/plan_list_global/plan_list_global_event.dart';

/// 测试实习计划集成页面
class TestPlanIntegrationScreen extends StatefulWidget {
  const TestPlanIntegrationScreen({Key? key}) : super(key: key);

  @override
  State<TestPlanIntegrationScreen> createState() => _TestPlanIntegrationScreenState();
}

class _TestPlanIntegrationScreenState extends State<TestPlanIntegrationScreen> {
  late PlanListGlobalBloc _planListBloc;

  @override
  void initState() {
    super.initState();
    _planListBloc = GetIt.instance<PlanListGlobalBloc>();
    // 加载实习计划列表
    _planListBloc.add(const LoadPlanListGlobalEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: AppBar(
        title: const Text('实习计划集成测试'),
        backgroundColor: const Color(0xFF2165F6),
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 课程头部组件 - 应该自动显示实习计划数据
          const CourseHeaderSection(),
          
          // 状态显示区域
          Expanded(
            child: BlocBuilder<PlanListGlobalBloc, PlanListGlobalState>(
              bloc: _planListBloc,
              builder: (context, state) {
                return Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '当前状态：',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10.h),
                      _buildStateInfo(state),
                      
                      SizedBox(height: 20.h),
                      
                      // 操作按钮
                      _buildActionButtons(state),
                      
                      SizedBox(height: 20.h),
                      
                      // 计划列表
                      if (state is PlanListGlobalLoadedState) ...[
                        Text(
                          '实习计划列表：',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Expanded(
                          child: _buildPlanList(state),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态信息
  Widget _buildStateInfo(PlanListGlobalState state) {
    String stateText;
    Color stateColor;
    
    if (state is PlanListGlobalInitialState) {
      stateText = '初始状态';
      stateColor = Colors.grey;
    } else if (state is PlanListGlobalLoadingState) {
      stateText = '加载中...';
      stateColor = Colors.blue;
    } else if (state is PlanListGlobalLoadedState) {
      stateText = '加载成功 - ${state.plans.length}个计划';
      stateColor = Colors.green;
      if (state.currentPlan != null) {
        stateText += '\n当前选中：${state.currentPlan!.planName}';
      }
    } else if (state is PlanListGlobalErrorState) {
      stateText = '加载失败：${state.message}';
      stateColor = Colors.red;
    } else if (state is PlanListGlobalRefreshSuccessState) {
      stateText = '刷新成功 - ${state.plans.length}个计划';
      stateColor = Colors.green;
    } else if (state is PlanListGlobalRefreshErrorState) {
      stateText = '刷新失败：${state.message}';
      stateColor = Colors.orange;
    } else {
      stateText = '未知状态：${state.runtimeType}';
      stateColor = Colors.purple;
    }
    
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: stateColor.withOpacity(0.1),
        border: Border.all(color: stateColor),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        stateText,
        style: TextStyle(
          fontSize: 14.sp,
          color: stateColor,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(PlanListGlobalState state) {
    return Wrap(
      spacing: 10.w,
      children: [
        ElevatedButton(
          onPressed: () {
            _planListBloc.add(const LoadPlanListGlobalEvent());
          },
          child: const Text('重新加载'),
        ),
        ElevatedButton(
          onPressed: () {
            _planListBloc.add(const RefreshPlanListGlobalEvent());
          },
          child: const Text('刷新'),
        ),
        if (state is PlanListGlobalLoadedState && state.plans.isNotEmpty)
          ElevatedButton(
            onPressed: () {
              // 切换到下一个计划
              final currentIndex = state.currentPlanId != null
                  ? state.plans.indexWhere((p) => p.planId == state.currentPlanId)
                  : -1;
              final nextIndex = (currentIndex + 1) % state.plans.length;
              final nextPlan = state.plans[nextIndex];
              _planListBloc.add(SelectCurrentPlanEvent(nextPlan.planId));
            },
            child: const Text('切换计划'),
          ),
      ],
    );
  }

  /// 构建计划列表
  Widget _buildPlanList(PlanListGlobalLoadedState state) {
    return ListView.builder(
      itemCount: state.plans.length,
      itemBuilder: (context, index) {
        final plan = state.plans[index];
        final isSelected = plan.planId == state.currentPlanId;
        
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF2165F6).withOpacity(0.1) : Colors.white,
            border: Border.all(
              color: isSelected ? const Color(0xFF2165F6) : Colors.grey.shade300,
            ),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      plan.planName,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? const Color(0xFF2165F6) : Colors.black87,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: const Color(0xFF2165F6),
                      size: 20.w,
                    ),
                ],
              ),
              SizedBox(height: 4.h),
              Text(
                '${plan.semester} • ${plan.statusText}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                'ID: ${plan.planId} • 年级: ${plan.grade}',
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// -----
/// file_approval_list_screen.dart
///
/// 文件审批列表页面，显示特定文件类型的审批列表
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/student_file_list_item.dart';
import 'package:flutter_demo/features/approval/presentation/pages/file_approval_preview_screen.dart';
import 'package:flutter_demo/features/approval/core/enums/approval_enums.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FileApprovalListScreen extends StatefulWidget {
  /// 文件类型ID
  final String fileId;

  const FileApprovalListScreen({
    Key? key,
    required this.fileId,
  }) : super(key: key);

  @override
  State<FileApprovalListScreen> createState() => _FileApprovalListScreenState();
}

class _FileApprovalListScreenState extends State<FileApprovalListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 分类后的学生文件列表
  late List<StudentFileInfo> _pendingStudents;
  late List<StudentFileInfo> _approvedStudents;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    // 生成示例学生文件数据
    _pendingStudents = _generateSampleStudentData(true);
    _approvedStudents = _generateSampleStudentData(false);
  }

  /// 生成示例学生文件数据
  List<StudentFileInfo> _generateSampleStudentData(bool isPending) {
    final status = isPending ? '待审批' : '已审批';
    final studentNames = isPending
        ? ['李成儒', '张小明', '王小红', '刘小强']
        : ['陈小华', '赵小丽'];

    return studentNames.asMap().entries.map((entry) {
      final index = entry.key;
      final name = entry.value;

      return StudentFileInfo(
        studentId: '${index + 1}'.padLeft(3, '0'),
        studentName: name,
        studentAvatar: AppConstants.avatar1,
        status: status,
        files: [
          FileInfo(
            fileId: 'tripartite_agreement',
            fileName: '三方协议',
            iconPath: 'assets/images/upload_pink_pdf_icon.png',
            onTap: () => _viewFile('tripartite_agreement', '三方协议', name, isPending),
          ),
          FileInfo(
            fileId: 'parent_notice',
            fileName: '告家长通知书',
            iconPath: 'assets/images/upload_pink_pdf_icon.png',
            onTap: () => _viewFile('parent_notice', '告家长通知书', name, isPending),
          ),
          FileInfo(
            fileId: 'insurance',
            fileName: '实习保险单',
            iconPath: 'assets/images/upload_purple_pdf_icon.png',
            onTap: () => _viewFile('insurance', '实习保险单', name, isPending),
          ),
          FileInfo(
            fileId: 'self_agreement',
            fileName: '自助协议申请表',
            iconPath: 'assets/images/upload_orange_pdf_icon.png',
            onTap: () => _viewFile('self_agreement', '自助协议申请表', name, isPending),
          ),
        ],
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: GetIt.instance<PlanListGlobalBloc>(),
      listener: (context, state) {
        // 当实习计划切换时，重新加载数据
        if (state is PlanListGlobalLoadedState) {
          _loadData();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: const CustomAppBar(
          title: '文件列表',
          centerTitle: true,
          showBackButton: true,
        ),
        body: Column(
          children: [
            // 课程头部 - 自动从全局状态获取实习计划数据
            const CourseHeaderSection(),

            // 标签栏
            ApprovalTabBar(
              controller: _tabController,
              pendingCount: _pendingStudents.length,
            ),

            // 页面内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPendingList(),
                  _buildApprovedList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 待审批列表
  Widget _buildPendingList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: _pendingStudents.length,
      itemBuilder: (context, index) {
        final studentFileInfo = _pendingStudents[index];
        return StudentFileListItem(
          studentFileInfo: studentFileInfo,
          isPending: true,
        );
      },
    );
  }

  // 已审批列表
  Widget _buildApprovedList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: _approvedStudents.length,
      itemBuilder: (context, index) {
        final studentFileInfo = _approvedStudents[index];
        return StudentFileListItem(
          studentFileInfo: studentFileInfo,
          isPending: false,
        );
      },
    );
  }

  /// 查看文件
  void _viewFile(String fileId, String fileName, String studentName, bool isPending) {
    // 根据状态确定审批状态
    ApprovalStatus approvalStatus;
    if (isPending) {
      approvalStatus = ApprovalStatus.pending;
    } else {
      // 这里可以根据实际业务逻辑判断是已通过还是已驳回
      // 暂时设为已通过，实际应该从数据中获取
      approvalStatus = ApprovalStatus.approved;
    }

    // 跳转到文件审批预览页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FileApprovalPreviewScreen(
          fileId: fileId,
          fileName: fileName,
          studentId: '001', // 实际应该传入学生ID
          studentName: studentName,
          approvalStatus: approvalStatus,
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
        ),
      ),
    ).then((result) {
      // 如果审批状态发生变化，刷新列表
      if (result == true) {
        _loadData();
      }
    });
  }
}

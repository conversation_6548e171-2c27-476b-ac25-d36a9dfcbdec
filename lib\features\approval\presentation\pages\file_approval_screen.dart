/// -----
/// file_approval_screen.dart
///
/// 教师端文件审批页面，显示需要审批的文件列表及审批进度统计
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/file_list_item.dart';
import 'package:flutter_demo/core/utils/pdf_navigator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class FileApprovalScreen extends StatelessWidget {
  const FileApprovalScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: const CustomAppBar(title: '文件审批'),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学年学期信息 - 自动从全局状态获取实习计划数据
          const CourseHeaderSection(),
          // 文件审批列表
          Expanded(
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 25.w),
              children: [
                FileListItem.withStatistics(
                  fileId: 'tripartite_agreement',
                  iconPath: 'assets/images/upload_blue_doc_icon.png',
                  fileName: '三方协议',
                  statisticsText: '15/25',
                  statisticsTextColor: const Color(0xFFB0B0B0),
                  onTap: () => _navigateToFileApprovalList(context, 'tripartite_agreement'),
                ),
                FileListItem.withStatistics(
                  fileId: 'parent_notice',
                  iconPath: 'assets/images/upload_pink_pdf_icon.png',
                  fileName: '告家长通知书',
                  statisticsText: '15/25',
                  statisticsTextColor: const Color(0xFFB0B0B0),
                  onTap: () => _navigateToFileApprovalList(context, 'parent_notice'),
                ),
                FileListItem.withStatistics(
                  fileId: 'insurance',
                  iconPath: 'assets/images/upload_purple_pdf_icon.png',
                  fileName: '实习保险单',
                  statisticsText: '15/25',
                  statisticsTextColor: const Color(0xFFB0B0B0),
                  onTap: () => _navigateToFileApprovalList(context, 'insurance'),
                ),
                FileListItem.withStatistics(
                  fileId: 'self_agreement',
                  iconPath: 'assets/images/upload_orange_pdf_icon.png',
                  fileName: '自助协议申请表',
                  statisticsText: '15/25',
                  statisticsTextColor: const Color(0xFFB0B0B0),
                  onTap: () => _navigateToFileApprovalList(context, 'self_agreement'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 导航到文件审批列表页面
  static void _navigateToFileApprovalList(BuildContext context, String fileId) {
    // 跳转到文件审批列表页面，使用GoRouter进行路由跳转，并携带参数fileId
    context.push('/file_approval_detail/$fileId');
  }


}
